#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import torchaudio
import numpy as np

def fix_raw_audio_to_wav(input_file, output_file, sample_rate=22050):
    """
    将原始PCM音频数据转换为标准WAV格式
    """
    try:
        # 读取原始音频数据
        with open(input_file, 'rb') as f:
            raw_audio = f.read()
        
        print(f"原始音频数据大小: {len(raw_audio)} bytes")
        
        # 转换为numpy数组
        audio_array = np.frombuffer(raw_audio, dtype=np.int16)
        print(f"音频样本数: {len(audio_array)}")
        
        # 转换为torch tensor并归一化
        audio_tensor = torch.from_numpy(audio_array).float()
        audio_tensor = audio_tensor / 32768.0  # 归一化到[-1, 1]
        audio_tensor = audio_tensor.unsqueeze(0)  # 添加batch维度
        
        # 保存为WAV文件
        torchaudio.save(output_file, audio_tensor, sample_rate)
        
        duration = len(audio_array) / sample_rate
        print(f"音频时长: {duration:.2f} 秒")
        print(f"已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 修复你刚才生成的音频文件
    success = fix_raw_audio_to_wav("my_tts_output.wav", "my_tts_output_fixed.wav")
    
    if success:
        print("音频文件修复成功！现在可以播放 my_tts_output_fixed.wav")
    else:
        print("音频文件修复失败")
