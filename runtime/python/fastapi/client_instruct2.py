# Copyright (c) 2024 Alibaba Inc (authors: <PERSON><PERSON>)
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import argparse
import logging
import requests
import torch
import torchaudio
import numpy as np


def main():
    url = "http://{}:{}/inference_{}".format(args.host, args.port, args.mode)
    if args.mode == 'sft':
        payload = {
            'tts_text': args.tts_text,
            'spk_id': args.spk_id
        }
        response = requests.request("GET", url, data=payload, stream=True)
    elif args.mode == 'zero_shot':
        payload = {
            'tts_text': args.tts_text,
            'prompt_text': args.prompt_text
        }
        files = [('prompt_wav', ('prompt_wav', open(args.prompt_wav, 'rb'), 'application/octet-stream'))]
        response = requests.request("GET", url, data=payload, files=files, stream=True)
    elif args.mode == 'cross_lingual':
        payload = {
            'tts_text': args.tts_text,
        }
        files = [('prompt_wav', ('prompt_wav', open(args.prompt_wav, 'rb'), 'application/octet-stream'))]
        response = requests.request("GET", url, data=payload, files=files, stream=True)
    elif args.mode == 'instruct':
        payload = {
            'tts_text': args.tts_text,
            'spk_id': args.spk_id,
            'instruct_text': args.instruct_text
        }
        response = requests.request("GET", url, data=payload, stream=True)
    elif args.mode == 'instruct2':
        payload = {
            'tts_text': args.tts_text,
            'instruct_text': args.instruct_text
        }
        files = [('prompt_wav', ('prompt_wav', open(args.prompt_wav, 'rb'), 'application/octet-stream'))]
        response = requests.request("POST", url, data=payload, files=files, stream=True)
    else:
        raise ValueError(f"Unsupported mode: {args.mode}")
    
    tts_audio = b''
    for r in response.iter_content(chunk_size=16000):
        tts_audio += r
    
    # 转换PCM数据为WAV格式
    tts_speech = torch.from_numpy(np.array(np.frombuffer(tts_audio, dtype=np.int16))).unsqueeze(dim=0).float()
    # 归一化到[-1, 1]范围
    tts_speech = tts_speech / 32768.0
    
    logging.info('save response to {}'.format(args.tts_wav))
    torchaudio.save(args.tts_wav, tts_speech, target_sr)
    logging.info('get response')
    print(f"音频已保存到: {args.tts_wav}")
    print(f"音频时长: {tts_speech.shape[1] / target_sr:.2f} 秒")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--host',
                        type=str,
                        default='0.0.0.0')
    parser.add_argument('--port',
                        type=int,
                        default='50000')
    parser.add_argument('--mode',
                        default='instruct2',
                        choices=['sft', 'zero_shot', 'cross_lingual', 'instruct', 'instruct2'],
                        help='request mode')
    parser.add_argument('--tts_text',
                        type=str,
                        default='今天天气真不错，适合出去走走')
    parser.add_argument('--spk_id',
                        type=str,
                        default='中文女')
    parser.add_argument('--prompt_text',
                        type=str,
                        default='我相信很多听友听到这首歌应该是在96年90年代的那个夏天。')
    parser.add_argument('--prompt_wav',
                        type=str,
                        default='../../../audio/calm.wav')
    parser.add_argument('--instruct_text',
                        type=str,
                        default='生气的说')
    parser.add_argument('--tts_wav',
                        type=str,
                        default='demo_instruct2.wav')
    args = parser.parse_args()
    prompt_sr, target_sr = 16000, 22050
    logging.basicConfig(level=logging.INFO)
    main()
