#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import torchaudio
import numpy as np
import sys
import os

def convert_raw_to_wav(input_file, output_file=None, sample_rate=22050):
    """
    将原始PCM音频数据转换为标准WAV格式
    """
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return False
        
    if output_file is None:
        # 自动生成输出文件名
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_fixed.wav"
    
    try:
        # 读取原始音频数据
        with open(input_file, 'rb') as f:
            raw_audio = f.read()
        
        print(f"原始音频数据大小: {len(raw_audio)} bytes")
        
        # 转换为numpy数组
        audio_array = np.frombuffer(raw_audio, dtype=np.int16)
        print(f"音频样本数: {len(audio_array)}")
        
        if len(audio_array) == 0:
            print("错误: 音频数据为空")
            return False
        
        # 转换为torch tensor并归一化
        audio_tensor = torch.from_numpy(audio_array).float()
        audio_tensor = audio_tensor / 32768.0  # 归一化到[-1, 1]
        audio_tensor = audio_tensor.unsqueeze(0)  # 添加batch维度
        
        # 保存为WAV文件
        torchaudio.save(output_file, audio_tensor, sample_rate)
        
        duration = len(audio_array) / sample_rate
        print(f"音频时长: {duration:.2f} 秒")
        print(f"已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

def main():
    if len(sys.argv) < 2:
        print("用法: python3 convert_raw_to_wav.py <输入文件> [输出文件] [采样率]")
        print("示例: python3 convert_raw_to_wav.py my_tts_output.wav")
        print("示例: python3 convert_raw_to_wav.py postman_output.wav postman_fixed.wav 22050")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    sample_rate = int(sys.argv[3]) if len(sys.argv) > 3 else 22050
    
    success = convert_raw_to_wav(input_file, output_file, sample_rate)
    
    if success:
        print("✅ 音频文件转换成功！")
    else:
        print("❌ 音频文件转换失败")

if __name__ == "__main__":
    main()
