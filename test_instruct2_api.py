#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import torch
import torchaudio
import numpy as np

def test_instruct2_api():
    """测试CosyVoice2的inference_instruct2 API"""
    
    # API配置
    url = "http://localhost:50000/inference_instruct2"
    
    # 请求参数 - 使用你的具体需求
    tts_text = "今天天气真不错，适合出去走走"
    instruct_text = "用平静温和的语调说话，就像播音员一样"
    audio_file_path = "audio/calm.wav"
    
    # 准备请求数据
    payload = {
        'tts_text': tts_text,
        'instruct_text': instruct_text
    }
    
    # 准备音频文件
    files = [
        ('prompt_wav', ('calm.wav', open(audio_file_path, 'rb'), 'audio/wav'))
    ]
    
    print(f"发送请求到: {url}")
    print(f"合成文本: {tts_text}")
    print(f"指令文本: {instruct_text}")
    print(f"参考音频: {audio_file_path}")
    
    try:
        # 发送POST请求
        response = requests.post(url, data=payload, files=files, stream=True)
        
        if response.status_code == 200:
            print("请求成功，正在接收音频数据...")
            
            # 接收音频数据
            tts_audio = b''
            for chunk in response.iter_content(chunk_size=16000):
                tts_audio += chunk
            
            # 转换为torch tensor
            tts_speech = torch.from_numpy(
                np.array(np.frombuffer(tts_audio, dtype=np.int16))
            ).unsqueeze(dim=0).float()

            # 归一化到[-1, 1]范围
            tts_speech = tts_speech / 32768.0

            # 保存音频文件
            output_file = 'output_instruct2.wav'
            torchaudio.save(output_file, tts_speech, 22050)  # CosyVoice2使用22050采样率
            
            print(f"音频已保存到: {output_file}")
            print(f"音频长度: {tts_speech.shape[1] / 22050:.2f} 秒")
            
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"请求出错: {str(e)}")
    
    finally:
        # 关闭文件
        files[0][1][1].close()

if __name__ == "__main__":
    test_instruct2_api()
